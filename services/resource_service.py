from typing import List, Dict, Any, Literal, Optional, Union
from datetime import datetime, timezone, timedelta
from models.resource import FileResource, ArticleResource, ResourceType, WebResource, ResourceStatus
from data_access.base import BaseRepository
from data_access.factory import RepositoryFactory
from services.resource_indexer import ResourceIndexer
from services.storage import storage_manager, StorageException, FileNotFoundError as StorageFileNotFoundError
from models.user import User
from langchain.schema import Document
from fastapi import HTTPException, UploadFile
from urllib.parse import urlparse
from config import settings
import asyncio
import tempfile
import logging
from uuid import UUID, uuid4
from pathlib import Path
import os
import io
import uuid

logger = logging.getLogger(__name__)

def convert_uuids_to_strings(obj):
    """Recursively convert UUID objects to strings in dictionaries and lists"""
    if isinstance(obj, dict):
        return {k: convert_uuids_to_strings(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_uuids_to_strings(item) for item in obj]
    elif isinstance(obj, (uuid.UUID, UUID)):
        return str(obj)
    else:
        return obj

class ResourceService:
    def __init__(self):
        """Initialize the resource service - repositories will be created on first use"""
        self.file_repo: Optional[BaseRepository[FileResource]] = None
        self.article_repo: Optional[BaseRepository[ArticleResource]] = None
        self.web_repo: Optional[BaseRepository[WebResource]] = None
        # Note: ResourceIndexer instances should be created with specific tenant_id in methods
        self._initialized = False

    async def initialize(self):
        """Initialize repositories asynchronously"""
        if self._initialized:
            return
            
        try:
            self.file_repo = await RepositoryFactory.create_file_resource_repository()
            self.article_repo = await RepositoryFactory.create_article_resource_repository()
            self.web_repo = await RepositoryFactory.create_web_resource_repository()
            self._initialized = True
            logger.info("ResourceService initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize ResourceService: {e}")
            raise

    async def _ensure_initialized(self):
        """Ensure the service is initialized before use"""
        if not self._initialized:
            await self.initialize()

    async def create_file_resource(self, resource: FileResource, current_user: User, tenant_id: Optional[str] = None) -> FileResource:
        """Create a new file resource"""
        await self._ensure_initialized()
        return await self.file_repo.create(resource, tenant_id)

    async def get_file_resource(self, resource_id: str, current_user: User) -> Optional[FileResource]:
        """Get a file resource by ID"""
        await self._ensure_initialized()
        return await self.file_repo.get_by_id(resource_id)

    async def list_file_resources(self, current_user: User, skip: int = 0, limit: int = 100, 
                                filter_dict: Optional[Dict] = None) -> List[FileResource]:
        """List file resources with filtering"""
        await self._ensure_initialized()
        
        # Build filter conditions including tenant filtering
        filters = filter_dict.copy() if filter_dict else {}
        if current_user and current_user.tenant_id:
            filters["tenant_id"] = current_user.tenant_id
            
        return await self.file_repo.get_all(skip, limit, filters if filters else None)

    async def update_file_resource(self, resource_id: str, resource: FileResource, current_user: User) -> Optional[FileResource]:
        """Update a file resource"""
        await self._ensure_initialized()
        return await self.file_repo.update(resource_id, resource)

    async def delete_file_resource(self, resource_id: str, current_user: User) -> bool:
        """Delete a file resource"""
        await self._ensure_initialized()
        return await self.file_repo.delete(resource_id)

    async def create_article_resource(self, resource: ArticleResource, current_user: User, tenant_id: Optional[str] = None) -> ArticleResource:
        """Create a new article resource"""
        await self._ensure_initialized()
        return await self.article_repo.create(resource, tenant_id)

    async def get_article_resource(self, resource_id: str, current_user: User) -> Optional[ArticleResource]:
        """Get an article resource by ID"""
        await self._ensure_initialized()
        return await self.article_repo.get_by_id(resource_id)

    async def list_article_resources(self, current_user: User, skip: int = 0, limit: int = 100, 
                                   filter_dict: Optional[Dict] = None) -> List[ArticleResource]:
        """List article resources with filtering"""
        await self._ensure_initialized()
        
        # Build filter conditions including tenant filtering
        filters = filter_dict.copy() if filter_dict else {}
        if current_user and current_user.tenant_id:
            filters["tenant_id"] = current_user.tenant_id
            
        return await self.article_repo.get_all(skip, limit, filters if filters else None)

    async def update_article_resource(self, resource_id: str, resource: ArticleResource, current_user: User) -> Optional[ArticleResource]:
        """Update an article resource"""
        await self._ensure_initialized()
        return await self.article_repo.update(resource_id, resource)

    async def delete_article_resource(self, resource_id: str, current_user: User) -> bool:
        """Delete an article resource"""
        await self._ensure_initialized()
        return await self.article_repo.delete(resource_id)

    async def create_web_resource(self, resource: WebResource, current_user: User, tenant_id: Optional[str] = None) -> WebResource:
        """Create a new web resource"""
        await self._ensure_initialized()
        return await self.web_repo.create(resource, tenant_id)

    async def get_web_resource(self, resource_id: str) -> Optional[WebResource]:
        """Get a web resource by ID"""
        await self._ensure_initialized()
        return await self.web_repo.get_by_id(resource_id)

    async def list_web_resources(self, current_user: User, skip: int = 0, limit: int = 100, 
                               filter_dict: Optional[Dict] = None) -> List[WebResource]:
        """List web resources with filtering"""
        await self._ensure_initialized()
        
        # Build filter conditions including tenant filtering
        filters = filter_dict.copy() if filter_dict else {}
        if current_user and current_user.tenant_id:
            filters["tenant_id"] = current_user.tenant_id
            
        return await self.web_repo.get_all(skip, limit, filters if filters else None)

    async def update_web_resource(self, resource_id: str, resource: WebResource, current_user: User) -> Optional[WebResource]:
        """Update a web resource"""
        await self._ensure_initialized()
        return await self.web_repo.update(resource_id, resource)

    async def delete_web_resource(self, resource_id: str, current_user: User) -> bool:
        """Delete a web resource"""
        await self._ensure_initialized()
        return await self.web_repo.delete(resource_id)

    async def update_resource_status(self, resource_type: str, resource_id: str, status: ResourceStatus,
                                   message: Optional[str] = None, progress_percentage: Optional[float] = None,
                                   pages_discovered: Optional[int] = None, pages_processed: Optional[int] = None,
                                   pages_failed: Optional[int] = None, estimated_completion_time: Optional[datetime] = None) -> bool:
        """Update status of any resource type with enhanced tracking"""
        await self._ensure_initialized()
        repo = self._get_repo_for_type(resource_type)
        if not repo:
            raise ValueError(f"Invalid resource type: {resource_type}")

        resource = await repo.get_by_id(resource_id)
        if not resource:
            return False

        # Update basic status
        resource.status = status
        resource.updated_at = datetime.now(timezone.utc)

        # Update enhanced status fields for WebResource
        if resource_type == "web" and hasattr(resource, 'status_message'):
            resource.status_message = message
            if progress_percentage is not None:
                resource.progress_percentage = max(0, min(100, progress_percentage))
            if pages_discovered is not None:
                resource.pages_discovered = pages_discovered
            if pages_processed is not None:
                resource.pages_processed = pages_processed
            if pages_failed is not None:
                resource.pages_failed = pages_failed
            if estimated_completion_time is not None:
                resource.estimated_completion_time = estimated_completion_time

            # Set processing timestamps
            if status == ResourceStatus.PROCESSING and not resource.processing_start_time:
                resource.processing_start_time = datetime.now(timezone.utc)
            elif status in [ResourceStatus.PROCESSED, ResourceStatus.FAILED]:
                resource.processing_end_time = datetime.now(timezone.utc)

        updated = await repo.update(resource_id, resource)
        return updated is not None

    def _get_repo_for_type(self, resource_type: str) -> Optional[BaseRepository]:
        """Get the appropriate repository for a resource type"""
        return {
            "file": self.file_repo,
            "article": self.article_repo,
            "web": self.web_repo
        }.get(resource_type.lower())

    def _get_collection_name(self, resource_type: str) -> str:
        """Get the collection name for a resource type"""
        return f"{resource_type.capitalize()}Document"
    
    async def process_file_resource(
        self, 
        file: UploadFile, 
        resource_id: str, 
        current_user: User, 
        tenant_id: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Process a file resource using the comprehensive indexer"""
        try:
            # Update resource status to processing
            await self.update_resource_status(
                resource_type="file",
                resource_id=resource_id,
                status=ResourceStatus.PROCESSING,
                message="Starting file processing"
            )
            
            # Create indexer for file resources
            indexer = ResourceIndexer(tenant_id, self._get_collection_name("File"))
            safe_metadata = convert_uuids_to_strings(metadata or {})
            
            # Process file using indexer
            result = await indexer.index_upload_file(
                file=file,
                tenant_id=tenant_id,
                metadata={
                    "metadata": safe_metadata,
                    "resource_id": resource_id,
                    "resource_type": "file",
                    "created_by": str(current_user.id),
                    "created_at": datetime.now(timezone.utc)
                }
            )
            
            # Update resource status to processed
            await self.update_resource_status(
                resource_type="file",
                resource_id=resource_id,
                status=ResourceStatus.PROCESSED,
                message="File processed and indexed successfully"
            )
            
            return convert_uuids_to_strings({
                "status": "success",
                "message": "File processed and indexed successfully",
                "resource_id": str(resource_id),
                "details": result
            })
            
        except Exception as e:
            # Update resource status to failed
            await self.update_resource_status(
                resource_type="file",
                resource_id=resource_id,
                status=ResourceStatus.FAILED,
                message=f"File processing failed: {str(e)}"
            )
            logger.error(f"Error processing file resource {resource_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"File processing failed: {str(e)}")

    async def process_article_resource(
        self, 
        text: str, 
        resource_id: str, 
        current_user: User, 
        tenant_id: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Process an article/text resource using the comprehensive indexer"""
        try:
            # Update resource status to processing
            await self.update_resource_status(
                resource_type="article",
                resource_id=resource_id,
                status=ResourceStatus.PROCESSING,
                message="Starting article processing"
            )
            
            # Create indexer for article resources
            indexer = ResourceIndexer(tenant_id, self._get_collection_name("Article"))
            safe_metadata = convert_uuids_to_strings(metadata or {})
            # Process text using indexer
            result = await indexer.index_text(
                text=text,
                metadata={
                    "metadata": safe_metadata,
                    "resource_id": resource_id,
                    "resource_type": "article",
                    "created_by": str(current_user.id),
                    "created_at": datetime.now(timezone.utc)
                }
            )
            
            # Update resource status to processed
            await self.update_resource_status(
                resource_type="article",
                resource_id=resource_id,
                status=ResourceStatus.PROCESSED,
                message="Article processed and indexed successfully"
            )
            
            return convert_uuids_to_strings({
                "status": "success",
                "message": "Article processed and indexed successfully",
                "resource_id": str(resource_id),
                "details": result
            })
            
        except Exception as e:
            # Update resource status to failed
            await self.update_resource_status(
                resource_type="article",
                resource_id=resource_id,
                status=ResourceStatus.FAILED,
                message=f"Article processing failed: {str(e)}"
            )
            logger.error(f"Error processing article resource {resource_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Article processing failed: {str(e)}")

    async def process_web_resource(
        self,
        url: str,
        resource_id: str,
        current_user: User,
        tenant_id: str,
        load_type: Literal["single", "recursive", "sitemap"] = "single",
        max_depth: Optional[int] = 2,
        exclude_dirs: Optional[List[str]] = None,
        sitemap_url: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        process_immediately: bool = True
    ) -> Dict[str, Any]:
        """Process a web resource with parent-child relationships for recursive/sitemap crawling"""
        try:
            # Update resource status to processing
            await self.update_resource_status(
                resource_type="web",
                resource_id=resource_id,
                status=ResourceStatus.PROCESSING,
                message="Starting web content processing"
            )

            # Get the root resource and mark it as root
            root_resource = await self.get_web_resource(resource_id)
            if root_resource:
                root_resource.is_root = True
                root_resource.root_id = UUID(resource_id)
                root_resource.crawl_strategy = load_type
                root_resource.processing_start_time = datetime.now(timezone.utc)
                await self.update_web_resource(resource_id, root_resource, current_user)

            # For sitemap processing, check if we should process in background
            if load_type == "sitemap":
                should_process_in_background = await self._should_process_sitemap_in_background(
                    sitemap_url or url, process_immediately
                )

                if should_process_in_background:
                    return await self._queue_sitemap_processing(
                        url=url,
                        resource_id=resource_id,
                        current_user=current_user,
                        tenant_id=tenant_id,
                        sitemap_url=sitemap_url,
                        metadata=metadata
                    )

            # Create indexer for web resources
            indexer = ResourceIndexer(tenant_id, self._get_collection_name("Web"))
            safe_metadata = convert_uuids_to_strings(metadata or {})

            # Process web content and get crawled URLs
            crawl_result = await self._process_web_content_with_relationships(
                indexer=indexer,
                url=url,
                resource_id=resource_id,
                tenant_id=tenant_id,
                current_user=current_user,
                load_type=load_type,
                max_depth=max_depth,
                exclude_dirs=exclude_dirs,
                sitemap_url=sitemap_url,
                metadata={
                    "metadata": safe_metadata,
                    "resource_id": resource_id,
                    "resource_type": "web",
                    "created_by": str(current_user.id),
                    "created_at": datetime.now(timezone.utc)
                }
            )
            
            # Update root resource with child count
            if root_resource and crawl_result.get("child_resources_created", 0) > 0:
                root_resource.child_count = crawl_result["child_resources_created"]
                await self.update_web_resource(resource_id, root_resource, current_user)
            
            # Update resource status to processed
            await self.update_resource_status(
                resource_type="web",
                resource_id=resource_id,
                status=ResourceStatus.PROCESSED,
                message=f"Web content processed successfully ({load_type} mode)"
            )
            
            return convert_uuids_to_strings({
                "status": "success",
                "message": "Web content processed and indexed successfully",
                "resource_id": str(resource_id),
                "load_type": load_type,
                "details": crawl_result,
                "child_resources_created": crawl_result.get("child_resources_created", 0),
                "total_pages_processed": crawl_result.get("total_pages_processed", 1)
            })
            
        except Exception as e:
            # Update resource status to failed
            await self.update_resource_status(
                resource_type="web",
                resource_id=resource_id,
                status=ResourceStatus.FAILED,
                message=f"Web processing failed: {str(e)}"
            )
            logger.error(f"Error processing web resource {resource_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Web processing failed: {str(e)}")

    async def _process_sitemap_with_child_resources(
        self,
        indexer: ResourceIndexer,
        sitemap_url: str,
        root_resource_id: str,
        tenant_id: str,
        current_user: User,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Process sitemap URL and create separate child resource entries for each discovered page"""
        try:
            # First, update the root resource to mark it as a sitemap root
            root_resource = await self.get_web_resource(root_resource_id)
            if root_resource:
                root_resource.is_root = True
                root_resource.crawl_strategy = "sitemap"
                root_resource.url = sitemap_url  # Ensure the root resource has the sitemap URL
                root_resource.sitemap_url = sitemap_url
                await self.web_repo.update(root_resource_id, root_resource)

            # Update status to indicate sitemap parsing
            await self.update_resource_status(
                resource_type="web",
                resource_id=root_resource_id,
                status=ResourceStatus.PROCESSING,
                message="Parsing sitemap and discovering pages...",
                progress_percentage=10.0
            )

            # Load content from sitemap
            documents = await indexer._load_web_content(
                url=sitemap_url,
                load_type="sitemap",
                sitemap_url=sitemap_url
            )

            child_resources_created = 0
            total_pages_discovered = len(documents)
            pages_failed = 0

            # Update status with discovered pages count
            await self.update_resource_status(
                resource_type="web",
                resource_id=root_resource_id,
                status=ResourceStatus.PROCESSING,
                message=f"Discovered {total_pages_discovered} pages. Creating child resources...",
                progress_percentage=20.0,
                pages_discovered=total_pages_discovered
            )

            # Estimate completion time (rough estimate: 2 seconds per page)
            estimated_seconds = total_pages_discovered * 2
            estimated_completion = datetime.now(timezone.utc) + timedelta(seconds=estimated_seconds)

            await self.update_resource_status(
                resource_type="web",
                resource_id=root_resource_id,
                status=ResourceStatus.PROCESSING,
                message=f"Processing {total_pages_discovered} pages...",
                progress_percentage=25.0,
                estimated_completion_time=estimated_completion
            )

            # Process each document and create child resources with enhanced error handling
            failed_urls = []
            for i, doc in enumerate(documents):
                child_url = doc.metadata.get("source", "")
                retry_count = 0
                max_retries = 3

                while retry_count <= max_retries:
                    try:
                        if not child_url or child_url == sitemap_url:
                            break

                        # Update progress
                        progress = 25.0 + (i / total_pages_discovered) * 60.0  # 25% to 85%
                        await self.update_resource_status(
                            resource_type="web",
                            resource_id=root_resource_id,
                            status=ResourceStatus.PROCESSING,
                            message=f"Processing page {i+1} of {total_pages_discovered}: {child_url}",
                            progress_percentage=progress,
                            pages_processed=child_resources_created,
                            pages_failed=pages_failed
                        )

                        # Create child WebResource entry with enhanced metadata
                        child_resource_data = {
                            "id": uuid4(),
                            "title": doc.metadata.get("title", f"Page from {child_url}"),
                            "description": f"Child page discovered from sitemap {sitemap_url}",
                            "url": child_url,
                            "content": doc.page_content[:1000],  # Store preview of content
                            "last_crawled": datetime.now(timezone.utc),
                            "crawl_depth": doc.metadata.get("sitemap_depth", 0),
                            "crawl_strategy": "sitemap",
                            "parent_id": UUID(root_resource_id),
                            "root_id": UUID(root_resource_id),
                            "is_root": False,
                            "child_count": 0,
                            "owner_id": current_user.id,
                            "tenant_id": tenant_id,
                            "status": ResourceStatus.PROCESSING,
                            "is_sitemap_child": True,
                            "sitemap_url": sitemap_url,
                            "crawl_order": i,
                            "sitemap_depth": doc.metadata.get("sitemap_depth", 0),
                            "parent_sitemap_url": doc.metadata.get("parent_sitemap_url", sitemap_url),
                            "retry_count": retry_count
                        }

                        # Add optional sitemap metadata
                        if doc.metadata.get("lastmod"):
                            child_resource_data["lastmod"] = doc.metadata["lastmod"]
                        if doc.metadata.get("changefreq"):
                            child_resource_data["changefreq"] = doc.metadata["changefreq"]
                        if doc.metadata.get("priority"):
                            child_resource_data["priority"] = doc.metadata["priority"]
                        if doc.metadata.get("sitemap_lastmod"):
                            child_resource_data["sitemap_lastmod"] = doc.metadata["sitemap_lastmod"]

                        child_resource = WebResource(**child_resource_data)

                        # Create the child resource in database
                        created_child = await self.create_web_resource(child_resource, current_user, tenant_id)
                        if created_child:
                            child_resources_created += 1

                            # Update document metadata to include child resource ID
                            doc.metadata.update({
                                "resource_id": str(created_child.id),
                                "parent_resource_id": root_resource_id,
                                "is_child_resource": True,
                                "is_sitemap_child": True
                            })

                            logger.info(f"Created child WebResource {created_child.id} for URL: {child_url}")

                            # Update child resource status to processed
                            await self.update_resource_status(
                                resource_type="web",
                                resource_id=str(created_child.id),
                                status=ResourceStatus.PROCESSED,
                                message="Child page processed successfully"
                            )
                            break  # Success, exit retry loop
                        else:
                            raise Exception("Failed to create child resource in database")

                    except Exception as e:
                        retry_count += 1
                        error_msg = f"Failed to create child WebResource for URL {child_url} (attempt {retry_count}/{max_retries + 1}): {str(e)}"
                        logger.error(error_msg)

                        if retry_count <= max_retries:
                            # Wait before retry with exponential backoff
                            await asyncio.sleep(2 ** retry_count)
                            logger.info(f"Retrying creation of child resource for {child_url}")
                        else:
                            # Max retries exceeded
                            pages_failed += 1
                            failed_urls.append({
                                "url": child_url,
                                "error": str(e),
                                "retry_count": retry_count - 1
                            })

                            # Update root resource with error details
                            if root_resource:
                                if not root_resource.error_details:
                                    root_resource.error_details = {}
                                if "failed_urls" not in root_resource.error_details:
                                    root_resource.error_details["failed_urls"] = []
                                root_resource.error_details["failed_urls"].append({
                                    "url": child_url,
                                    "error": str(e),
                                    "timestamp": datetime.now(timezone.utc).isoformat()
                                })
                            break

            # Update root resource child count and final status
            if root_resource:
                root_resource.child_count = child_resources_created
                await self.web_repo.update(root_resource_id, root_resource)

            # Update progress to indexing phase
            await self.update_resource_status(
                resource_type="web",
                resource_id=root_resource_id,
                status=ResourceStatus.PROCESSING,
                message="Indexing processed pages...",
                progress_percentage=85.0,
                pages_processed=child_resources_created,
                pages_failed=pages_failed
            )

            # Index all documents
            base_metadata = {
                "tenant_id": tenant_id,
                "processed_by": str(current_user.id),
                "source_url": sitemap_url,
                "load_type": "sitemap",
                "is_sitemap_processing": True,
                **(metadata or {})
            }

            for doc in documents:
                doc.metadata.update(base_metadata)

            # Index documents
            result = await indexer.index_documents(
                documents=documents,
                cleanup_mode="incremental"
            )

            # Final status update
            success_rate = (child_resources_created / total_pages_discovered * 100) if total_pages_discovered > 0 else 0
            final_message = f"Sitemap processing complete. {child_resources_created} pages processed successfully"
            if pages_failed > 0:
                final_message += f", {pages_failed} pages failed"
            final_message += f" (Success rate: {success_rate:.1f}%)"

            await self.update_resource_status(
                resource_type="web",
                resource_id=root_resource_id,
                status=ResourceStatus.PROCESSED,
                message=final_message,
                progress_percentage=100.0,
                pages_processed=child_resources_created,
                pages_failed=pages_failed
            )

            return {
                **result,
                "child_resources_created": child_resources_created,
                "total_pages_processed": total_pages_discovered,
                "pages_successful": child_resources_created,
                "pages_failed": pages_failed,
                "success_rate": success_rate,
                "sitemap_url": sitemap_url,
                "processing_type": "sitemap_with_children"
            }

        except Exception as e:
            logger.error(f"Error processing sitemap with child resources: {str(e)}")

            # Update root resource with error information
            if root_resource:
                root_resource.status = ResourceStatus.FAILED
                root_resource.last_error = str(e)
                root_resource.processing_end_time = datetime.now(timezone.utc)
                if not root_resource.error_details:
                    root_resource.error_details = {}
                root_resource.error_details["processing_error"] = {
                    "error": str(e),
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
                await self.web_repo.update(root_resource_id, root_resource)

            raise

    async def _should_process_sitemap_in_background(
        self,
        sitemap_url: str,
        process_immediately: bool = True
    ) -> bool:
        """Determine if sitemap processing should be done in background"""
        if not process_immediately:
            return True

        try:
            # Quick check of sitemap size using lightweight URL counting
            url_count = await self._count_sitemap_urls(sitemap_url)

            # Process in background if more than 50 URLs
            if url_count > 50:
                logger.info(f"Sitemap has {url_count} URLs, processing in background")
                return True

            return False

        except Exception as e:
            logger.warning(f"Could not determine sitemap size, processing immediately: {str(e)}")
            return False

    async def _count_sitemap_urls(self, sitemap_url: str, depth: int = 0) -> int:
        """Lightweight function to count URLs in a sitemap without loading content"""
        try:
            import xml.etree.ElementTree as ET
            import requests
            from urllib.parse import urljoin

            # Prevent infinite recursion
            if depth > 5:
                return 0

            # Fetch sitemap XML
            response = requests.get(
                sitemap_url,
                timeout=30,
                headers={
                    "User-Agent": "Mozilla/5.0 (compatible; AssivyBot/1.0; +https://assivy.com/bot)"
                },
                verify=False
            )
            response.raise_for_status()

            # Parse XML
            root = ET.fromstring(response.content)

            # Standard sitemap namespace
            namespaces = {
                'sitemap': 'http://www.sitemaps.org/schemas/sitemap/0.9'
            }

            url_count = 0

            # Check if this is a sitemap index
            sitemap_elements = root.findall('.//sitemap:sitemap', namespaces)
            if not sitemap_elements:
                sitemap_elements = root.findall('.//sitemap')

            if sitemap_elements:
                # This is a sitemap index - recursively count child sitemaps
                for sitemap_elem in sitemap_elements:
                    loc_elem = sitemap_elem.find('sitemap:loc', namespaces) or sitemap_elem.find('loc')
                    if loc_elem is not None and loc_elem.text:
                        child_sitemap_url = urljoin(sitemap_url, loc_elem.text.strip())
                        url_count += await self._count_sitemap_urls(child_sitemap_url, depth + 1)
            else:
                # This is a regular sitemap - count URLs
                url_elements = root.findall('.//sitemap:url', namespaces)
                if not url_elements:
                    url_elements = root.findall('.//url')
                url_count = len(url_elements)

            return url_count

        except Exception as e:
            logger.warning(f"Error counting sitemap URLs: {str(e)}")
            return 0

    async def _queue_sitemap_processing(
        self,
        url: str,
        resource_id: str,
        current_user: User,
        tenant_id: str,
        sitemap_url: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Queue sitemap processing for background execution"""
        try:
            # Update status to indicate background processing
            await self.update_resource_status(
                resource_type="web",
                resource_id=resource_id,
                status=ResourceStatus.PROCESSING,
                message="Queued for background processing - large sitemap detected",
                progress_percentage=5.0
            )

            # Create background task
            task = asyncio.create_task(
                self._process_sitemap_background(
                    url=url,
                    resource_id=resource_id,
                    current_user=current_user,
                    tenant_id=tenant_id,
                    sitemap_url=sitemap_url,
                    metadata=metadata
                )
            )

            return {
                "status": "processing",
                "message": "Large sitemap queued for background processing",
                "method": "background",
                "task_id": id(task),
                "estimated_pages": "calculating...",
                "processing_type": "sitemap_background"
            }

        except Exception as e:
            logger.error(f"Error queuing sitemap processing: {str(e)}")
            raise

    async def _process_sitemap_background(
        self,
        url: str,
        resource_id: str,
        current_user: User,
        tenant_id: str,
        sitemap_url: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """Background sitemap processing task"""
        try:
            logger.info(f"Starting background sitemap processing for resource {resource_id}")

            # Create indexer for web resources
            indexer = ResourceIndexer(tenant_id, self._get_collection_name("Web"))
            safe_metadata = convert_uuids_to_strings(metadata or {})

            # Process sitemap with child resources
            await self._process_sitemap_with_child_resources(
                indexer=indexer,
                sitemap_url=sitemap_url or url,
                root_resource_id=resource_id,
                tenant_id=tenant_id,
                current_user=current_user,
                metadata={
                    "metadata": safe_metadata,
                    "resource_id": resource_id,
                    "resource_type": "web",
                    "created_by": str(current_user.id),
                    "created_at": datetime.now(timezone.utc),
                    "processing_method": "background"
                }
            )

            logger.info(f"Background sitemap processing completed for resource {resource_id}")

        except Exception as e:
            logger.error(f"Error in background sitemap processing for resource {resource_id}: {str(e)}")

            # Update resource status to failed
            await self.update_resource_status(
                resource_type="web",
                resource_id=resource_id,
                status=ResourceStatus.FAILED,
                message=f"Background processing failed: {str(e)}",
                progress_percentage=0
            )

    async def get_web_resource_children(self, parent_resource_id: str) -> List[WebResource]:
        """Get all child resources for a parent web resource"""
        await self._ensure_initialized()
        try:
            # Query for child resources
            filter_conditions = {
                "parent_id": parent_resource_id
            }
            children = await self.web_repo.get_all(query_conditions=filter_conditions)
            return children
        except Exception as e:
            logger.error(f"Error getting web resource children: {str(e)}")
            return []

    async def search_resources(
        self,
        query: str,
        tenant_id: str,
        resource_type: ResourceType,
        current_user: User,
        k: int = 4,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Document]:
        """Search resources using the vector store"""
        try:
            # Create indexer for the specified resource type
            indexer = ResourceIndexer(tenant_id, self._get_collection_name(resource_type.value))
            
            # Add user context to filters
            search_filters = {
                "tenant_id": tenant_id,
                **(filters or {})
            }
            
            # Search documents
            results = await indexer.search_documents(
                query=query,
                tenant_id=tenant_id,
                k=k,
                filters=search_filters
            )
            
            return results
            
        except Exception as e:
            logger.error(f"Error searching resources: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")

    async def delete_resource_from_index(
        self,
        resource_id: str,
        resource_type: ResourceType,
        current_user: User,
        tenant_id: str
    ) -> Dict[str, Any]:
        """Delete a resource from the vector index"""
        try:
            # Create indexer for the specified resource type
            indexer = ResourceIndexer(tenant_id, self._get_collection_name(resource_type.value))
            
            # Delete from index
            result = await indexer.delete_documents(
                tenant_id=tenant_id,
                doc_ids=[resource_id],
                filters={"tenant_id": tenant_id}
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Error deleting resource from index: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Index deletion failed: {str(e)}")

    async def get_resource_stats(
        self,
        resource_type: ResourceType,
        tenant_id: str,
        current_user: User
    ) -> Dict[str, Any]:
        """Get statistics for resources"""
        try:
            # Get database stats
            repo = self._get_repo_for_type(resource_type.value)
            db_stats = await repo.get_stats(tenant_id) if hasattr(repo, 'get_stats') else {}
            
            # Get vector store stats
            indexer = ResourceIndexer(tenant_id, self._get_collection_name(resource_type.value))
            vector_stats = await indexer.get_collection_stats(tenant_id)
            
            return {
                "resource_type": resource_type.value,
                "tenant_id": tenant_id,
                "database_stats": db_stats,
                "vector_stats": vector_stats,
                "collection_name": self._get_collection_name(resource_type.value)
            }
            
        except Exception as e:
            logger.error(f"Error getting resource stats: {str(e)}")
            return {
                "resource_type": resource_type.value,
                "tenant_id": tenant_id,
                "error": str(e)
            }

    async def upload_and_process_file(
        self,
        file: UploadFile,
        current_user: User,
        tenant_id: str,
        title: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        process_immediately: bool = False
    ) -> Dict[str, Any]:
        """
        Upload file to storage and process it for indexing.
        
        Args:
            file: The uploaded file
            current_user: Current user
            tenant_id: Tenant ID
            title: Optional title for the resource (defaults to filename if not provided)
            metadata: Additional metadata
            process_immediately: Force immediate processing
            
        Returns:
            Dict containing file storage info and processing results
        """
        try:
            # Validate file
            await self._validate_file(file)
            
            # Use provided title or fallback to filename
            resource_title = title or file.filename
            
            # Create file resource record
            file_resource = FileResource(
                title=resource_title,  # Use the provided title or filename
                filename=file.filename,
                file_type=file.content_type,
                size=0,  # Will be updated after upload
                tenant_id=tenant_id,
                owner_id=current_user.id,
                status=ResourceStatus.PENDING,
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc)
            )
            
            # Save resource to database
            saved_resource = await self.create_file_resource(file_resource, current_user)
            resource_id = str(saved_resource.id)
            
            # Upload file to storage
            file_stream = io.BytesIO(await file.read())
            storage_key = await storage_manager.upload_file(
                file_content=file_stream,
                tenant_id=tenant_id,
                filename=file.filename,
                file_type="documents",  # Changed from "files" to avoid duplication in URL path
                content_type=file.content_type,
                metadata={
                    "Resource-Id": resource_id,
                    "Uploaded-By": str(current_user.id),
                    **(metadata or {})
                }
            )
            
            # Update resource with storage info
            saved_resource.storage_key = storage_key
            saved_resource.size = len(file_stream.getvalue())
            saved_resource.status = ResourceStatus.PROCESSING
            await self.update_file_resource(resource_id, saved_resource, current_user)
            
            # Determine processing strategy
            file_size_mb = saved_resource.size / (1024 * 1024)
            should_process_immediately = (
                process_immediately or 
                file_size_mb < getattr(settings, 'small_file_threshold_mb', 5) or
                file.content_type.startswith('text/')
            )
            
            processing_result = {}
            
            if should_process_immediately:
                # Process immediately
                logger.info(f"Processing file immediately: {file.filename} ({file_size_mb:.2f}MB)")
                processing_result = await self._process_file_immediate(
                    storage_key, resource_id, tenant_id, current_user, metadata
                )
            else:
                # Queue for background processing
                logger.info(f"Queuing file for background processing: {file.filename} ({file_size_mb:.2f}MB)")
                processing_result = await self._queue_file_processing(
                    storage_key, resource_id, tenant_id, current_user, metadata
                )
            
            return {
                "resource_id": resource_id,
                "storage_key": storage_key,
                "filename": file.filename,
                "size": saved_resource.size,
                "content_type": file.content_type,
                "processing_strategy": "immediate" if should_process_immediately else "background",
                "processing_result": processing_result,
                "status": "success"
            }
            
        except Exception as e:
            logger.error(f"File upload and processing failed: {str(e)}")
            # Update resource status if we have a resource_id
            if 'resource_id' in locals():
                await self.update_resource_status(
                    resource_type="file",
                    resource_id=resource_id,
                    status=ResourceStatus.FAILED,
                    
                    message=f"Upload failed: {str(e)}"
                )
            raise HTTPException(status_code=500, detail=f"File processing failed: {str(e)}")
    
    async def _validate_file(self, file: UploadFile) -> None:
        """Validate uploaded file"""
        from config import settings
        
        # Check file size
        file_content = await file.read()
        file_size_mb = len(file_content) / (1024 * 1024)
        max_size_mb = getattr(settings, 'storage_max_file_size_mb', 100)
        
        if file_size_mb > max_size_mb:
            raise HTTPException(
                status_code=413,
                detail=f"File too large. Maximum size: {max_size_mb}MB"
            )
        
        # Check file extension
        file_ext = Path(file.filename).suffix.lower()
        allowed_extensions = getattr(settings, 'storage_allowed_extensions', [])
        
        if allowed_extensions and file_ext not in allowed_extensions:
            raise HTTPException(
                status_code=415,
                detail=f"File type not supported. Allowed: {', '.join(allowed_extensions)}"
            )
        
        # Reset file position
        await file.seek(0)
    
    async def _process_file_immediate(
        self,
        storage_key: str,
        resource_id: str,
        tenant_id: str,
        current_user: User,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Process file immediately using storage manager"""
        try:
            # Update status to processing
            await self.update_resource_status(
                resource_type="file",
                resource_id=resource_id,
                status=ResourceStatus.PROCESSING,
                message="Processing file content"
            )
            
            # Create indexer for file resources
            indexer = ResourceIndexer(tenant_id, self._get_collection_name("File"))
            
            # Process the file directly from storage
            result = await indexer.index_storage_file(
                storage_key=storage_key,
                tenant_id=tenant_id,
                resource_id=resource_id,  # Pass resource_id as direct parameter
                metadata={
                    "resource_type": "file",
                    "processed_by": str(current_user.id),
                    "created_at": datetime.now(timezone.utc).isoformat()
                }
            )
            
            # Update status to completed
            await self.update_resource_status(
                resource_type="file",
                resource_id=resource_id,
                status=ResourceStatus.PROCESSED,
                message="Successfully processed document"
            )
            
            return {
                "status": "completed",
                "processing_time": result.get('processing_time', 0),
                "method": "immediate",
                "storage_info": result.get("storage_info", {})
            }
        
        except Exception as e:
            logger.error(f"Immediate file processing failed: {str(e)}")
            await self.update_resource_status(
                resource_type="file",
                resource_id=resource_id,
                status=ResourceStatus.FAILED,
                message=f"Processing failed: {str(e)}"
            )
            return {
                "status": "failed",
                "error": str(e),
                "method": "immediate"
            }
    
    async def _queue_file_processing(
        self,
        storage_key: str,
        resource_id: str,
        tenant_id: str,
        current_user: User,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Queue file for background processing"""
        try:
            # Update status to queued
            await self.update_resource_status(
                resource_type="file",
                resource_id=resource_id,
                status=ResourceStatus.PROCESSING,
                
                message="Processing in background"
            )
            
            # Create background task
            task = asyncio.create_task(
                self._process_file_background(
                    storage_key, resource_id, tenant_id, current_user, metadata
                )
            )
            
            return {
                "status": "processing",
                "message": "File processing in background",
                "method": "background",
                "task_id": id(task)  # Simple task tracking
            }
            
        except Exception as e:
            logger.error(f"Failed to queue file processing: {str(e)}")
            await self.update_resource_status(
                resource_type="file",
                resource_id=resource_id,
                status=ResourceStatus.FAILED,
                
                message=f"Queueing failed: {str(e)}"
            )
            return {
                "status": "failed",
                "error": str(e),
                "method": "background"
            }
    
    async def _process_file_background(
        self,
        storage_key: str,
        resource_id: str,
        tenant_id: str,
        current_user: User,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """Background file processing task using storage manager"""
        try:
            logger.info(f"Starting background processing for resource {resource_id}")
            
            # Update status to processing
            await self.update_resource_status(
                resource_type="file",
                resource_id=resource_id,
                status=ResourceStatus.PROCESSING,
                message="Background processing started"
            )
            
            # Create indexer for file resources
            indexer = ResourceIndexer(tenant_id, self._get_collection_name("File"))
            
            # Process the file directly from storage
            result = await indexer.index_storage_file(
                storage_key=storage_key,
                tenant_id=tenant_id,
                resource_id=resource_id,  # Pass resource_id as direct parameter
                metadata={
                    "resource_type": "file",
                    "processed_by": str(current_user.id),
                    "processing_type": "background",
                    "created_at": datetime.now(timezone.utc).isoformat()
                }
            )
            
            # Update status to completed
            await self.update_resource_status(
                resource_type="file",
                resource_id=resource_id,
                status=ResourceStatus.PROCESSED,
                message="Background processing completed successfully"
            )
            
            logger.info(f"Background processing completed for resource {resource_id}")
        
        except Exception as e:
            logger.error(f"Background file processing failed for resource {resource_id}: {str(e)}")
            await self.update_resource_status(
                resource_type="file",
                resource_id=resource_id,
                status=ResourceStatus.FAILED,
                message=f"Background processing failed: {str(e)}"
            )
    
    async def download_file(self, resource_id: str, current_user: User) -> Dict[str, Any]:
        """Download a file from storage"""
        try:
            # Get resource
            resource = await self.get_file_resource(resource_id, current_user)
            if not resource:
                raise HTTPException(status_code=404, detail="Resource not found")
            
            if not hasattr(resource, 'storage_key') or not resource.storage_key:
                raise HTTPException(status_code=404, detail="File not found in storage")
            
            # Get file content
            file_content = await storage_manager.download_file(resource.storage_key)
            
            return {
                "content": file_content,
                "filename": resource.filename,
                "content_type": resource.file_type,
                "size": resource.size
            }
            
        except StorageFileNotFoundError:
            raise HTTPException(status_code=404, detail="File not found in storage")
        except StorageException as e:
            raise HTTPException(status_code=500, detail=f"Storage error: {str(e)}")
        except Exception as e:
            logger.error(f"File download failed: {str(e)}")
            raise HTTPException(status_code=500, detail="Download failed")
    
    async def get_file_download_url(self, resource_id: str, current_user: User, expiration: int = 3600) -> str:
        """Get a presigned download URL for a file"""
        try:
            # Get resource
            resource = await self.get_file_resource(resource_id, current_user)
            if not resource:
                raise HTTPException(status_code=404, detail="Resource not found")
            
            if not hasattr(resource, 'storage_key') or not resource.storage_key:
                raise HTTPException(status_code=404, detail="File not found in storage")
            
            # Generate presigned URL
            url = await storage_manager.generate_presigned_url(
                resource.storage_key,
                expiration=expiration,
                method="GET"
            )
            
            return url
            
        except StorageException as e:
            raise HTTPException(status_code=500, detail=f"Storage error: {str(e)}")
        except Exception as e:
            logger.error(f"Presigned URL generation failed: {str(e)}")
            raise HTTPException(status_code=500, detail="URL generation failed")
    
    async def delete_file_from_storage(self, resource_id: str, current_user: User) -> bool:
        """Delete a file from both database and storage"""
        try:
            # Get resource
            resource = await self.get_file_resource(resource_id, current_user)
            if not resource:
                return False
            
            # Delete from storage if storage key exists
            if hasattr(resource, 'storage_key') and resource.storage_key:
                try:
                    await storage_manager.delete_file(resource.storage_key)
                except StorageException as e:
                    logger.warning(f"Failed to delete file from storage: {str(e)}")
            
            # Delete from database
            return await self.delete_file_resource(resource_id, current_user)
            
        except Exception as e:
            logger.error(f"File deletion failed: {str(e)}")
            return False
    
    async def get_storage_stats(self, tenant_id: str, current_user: User) -> Dict[str, Any]:
        """Get storage statistics for a tenant"""
        try:
            await self._ensure_initialized()
            
            # Get basic stats from repositories instead of storage manager
            file_resources = await self.file_repo.get_all(0, 1000)  # Get first 1000
            article_resources = await self.article_repo.get_all(0, 1000)
            web_resources = await self.web_repo.get_all(0, 1000)
            
            total_files = len(file_resources)
            total_articles = len(article_resources)
            total_web_resources = len(web_resources)
            
            # Calculate estimated total size from file resources
            total_size = 0
            for file_resource in file_resources:
                if hasattr(file_resource, 'size') and file_resource.size:
                    total_size += file_resource.size
            
            return {
                "tenant_id": tenant_id,
                "total_files": total_files,
                "total_articles": total_articles,
                "total_web_resources": total_web_resources,
                "total_resources": total_files + total_articles + total_web_resources,
                "estimated_total_size": total_size,
                "storage_health": "available"
            }
            
        except Exception as e:
            logger.error(f"Error getting storage stats: {str(e)}")
            # Return basic stats even on error
            return {
                "tenant_id": tenant_id,
                "total_files": 0,
                "total_articles": 0,
                "total_web_resources": 0,
                "total_resources": 0,
                "estimated_total_size": 0,
                "storage_health": "error",
                "error": str(e)
            }
    
    def convert_uuids_to_strings(obj):
        """Recursively convert UUID objects to strings in dictionaries and lists"""
        if isinstance(obj, dict):
            return {k: convert_uuids_to_strings(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [convert_uuids_to_strings(item) for item in obj]
        elif isinstance(obj, (uuid.UUID, UUID)):
            return str(obj)
        else:
            return obj

    async def reindex_resources(
        self,
        resource_type: ResourceType,
        tenant_id: str,
        current_user: User,
        force_reindex: bool = False
    ) -> Dict[str, Any]:
        """
        Reindex all resources of a specific type for a tenant.
        
        Args:
            resource_type: Type of resources to reindex
            tenant_id: Tenant ID
            current_user: Current user
            force_reindex: Force reindex even if already indexed
            
        Returns:
            Dict containing reindex results
        """
        try:
            await self._ensure_initialized()
            
            # Get repository for the resource type
            repo = self._get_repo_for_type(resource_type.value)
            if not repo:
                raise HTTPException(status_code=400, detail=f"Unsupported resource type: {resource_type.value}")
            
            # Get all resources for the tenant
            resources = await repo.list_by_tenant(tenant_id, skip=0, limit=1000)
            
            if not resources:
                return {
                    "status": "success",
                    "message": "No resources found to reindex",
                    "resource_type": resource_type.value,
                    "tenant_id": tenant_id,
                    "total_resources": 0,
                    "reindexed": 0,
                    "skipped": 0,
                    "failed": 0
                }
            
            # Create indexer for the resource type
            indexer = ResourceIndexer(tenant_id, self._get_collection_name(resource_type.value))
            
            reindexed_count = 0
            skipped_count = 0
            failed_count = 0
            failed_resources = []
            
            for resource in resources:
                try:
                    # Skip if already indexed and not forcing reindex
                    if not force_reindex and resource.status == ResourceStatus.PROCESSED:
                        skipped_count += 1
                        continue
                    
                    # Update status to processing
                    await self.update_resource_status(
                        resource_type=resource_type.value,
                        resource_id=str(resource.id),
                        status=ResourceStatus.PROCESSING,
                        message="Reindexing resource"
                    )
                    
                    # Process based on resource type
                    if resource_type == ResourceType.FILE:
                        if hasattr(resource, 'storage_key') and resource.storage_key:
                            async with storage_manager.temporary_file(resource.storage_key) as temp_file_path:
                                result = await indexer.index_file(
                                    file_path=temp_file_path,
                                    tenant_id=tenant_id,
                                    metadata={
                                        "resource_id": str(resource.id),
                                        "storage_key": resource.storage_key,
                                        "reindexed_by": str(current_user.id),
                                        "reindex_timestamp": datetime.now(timezone.utc).isoformat()
                                    },
                                    cleanup_mode="incremental"
                                )
                    
                    elif resource_type == ResourceType.ARTICLE:
                        if hasattr(resource, 'content') and resource.content:
                            result = await indexer.index_text(
                                text=resource.content,
                                metadata={
                                    "resource_id": str(resource.id),
                                    "resource_type": "article",
                                    "reindexed_by": str(current_user.id),
                                    "reindex_timestamp": datetime.now(timezone.utc).isoformat()
                                },
                                cleanup_mode="incremental"
                            )
                    
                    elif resource_type == ResourceType.WEB:
                        if hasattr(resource, 'url') and resource.url:
                            result = await indexer.index_web_content(
                                url=resource.url,
                                tenant_id=tenant_id,
                                metadata={
                                    "resource_id": str(resource.id),
                                    "reindexed_by": str(current_user.id),
                                    "reindex_timestamp": datetime.now(timezone.utc).isoformat()
                                },
                                cleanup_mode="incremental"
                            )
                    
                    # Update status to indexed
                    await self.update_resource_status(
                        resource_type=resource_type.value,
                        resource_id=str(resource.id),
                        status=ResourceStatus.PROCESSED,
                        message="Successfully reindexed"
                    )
                    
                    reindexed_count += 1
                    
                except Exception as e:
                    logger.error(f"Failed to reindex resource {resource.id}: {str(e)}")
                    failed_count += 1
                    failed_resources.append({
                        "resource_id": str(resource.id),
                        "error": str(e)
                    })
                    
                    # Update status to failed
                    await self.update_resource_status(
                        resource_type=resource_type.value,
                        resource_id=str(resource.id),
                        status=ResourceStatus.FAILED,
                        message=f"Reindex failed: {str(e)}"
                    )
            
            return {
                "status": "success",
                "message": f"Reindex completed for {resource_type.value} resources",
                "resource_type": resource_type.value,
                "tenant_id": tenant_id,
                "total_resources": len(resources),
                "reindexed": reindexed_count,
                "skipped": skipped_count,
                "failed": failed_count,
                "failed_resources": failed_resources
            }
            
        except Exception as e:
            logger.error(f"Error reindexing resources: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Reindex failed: {str(e)}")

    async def reindex_single_resource(
        self,
        resource_type: ResourceType,
        resource_id: str,
        current_user: User
    ) -> Dict[str, Any]:
        """
        Reindex a single resource.
        
        Args:
            resource_type: Type of the resource
            resource_id: Resource ID
            current_user: Current user
            
        Returns:
            Dict containing reindex result
        """
        try:
            await self._ensure_initialized()
            
            # Get repository for the resource type
            repo = self._get_repo_for_type(resource_type.value)
            if not repo:
                raise HTTPException(status_code=400, detail=f"Unsupported resource type: {resource_type.value}")
            
            # Get the resource
            resource = await repo.get_by_id(resource_id)
            if not resource:
                raise HTTPException(status_code=404, detail="Resource not found")
            
            # Get tenant_id from the resource
            tenant_id = str(resource.tenant_id)
            
            # Verify user has access to this tenant
            if current_user.tenant_id and str(current_user.tenant_id) != tenant_id:
                raise HTTPException(status_code=403, detail="Access denied")
            
            # Create indexer for the resource type
            indexer = ResourceIndexer(tenant_id, self._get_collection_name(resource_type.value))
            
            # Update status to processing
            await self.update_resource_status(
                resource_type=resource_type.value,
                resource_id=resource_id,
                status=ResourceStatus.PROCESSING,
                message="Reindexing resource"
            )
            
            try:
                # Process based on resource type
                if resource_type == ResourceType.FILE:
                    if hasattr(resource, 'storage_key') and resource.storage_key:
                        result = await indexer.index_storage_file(
                            storage_key=resource.storage_key,
                            tenant_id=tenant_id,
                            resource_id=resource_id,  # Pass resource_id as direct parameter
                            metadata={
                                "resource_type": "file",
                                "reindexed_by": str(current_user.id),
                                "reindex_timestamp": datetime.now(timezone.utc).isoformat()
                            },
                            cleanup_mode="incremental"
                        )
                    else:
                        raise HTTPException(status_code=400, detail="File resource has no storage key")
                
                elif resource_type == ResourceType.ARTICLE:
                    if hasattr(resource, 'content') and resource.content:
                        result = await indexer.index_text(
                            text=resource.content,
                            metadata={
                                "resource_id": resource_id,
                                "resource_type": "article",
                                "reindexed_by": str(current_user.id),
                                "reindex_timestamp": datetime.now(timezone.utc).isoformat()
                            },
                            cleanup_mode="incremental"
                        )
                    else:
                        raise HTTPException(status_code=400, detail="Article resource has no content")
                
                elif resource_type == ResourceType.WEB:
                    if hasattr(resource, 'url') and resource.url:
                        result = await indexer.index_web_content(
                            url=resource.url,
                            tenant_id=tenant_id,
                            resource_id=resource_id,
                            metadata={
                                "resource_id": resource_id,
                                "reindexed_by": str(current_user.id),
                                "reindex_timestamp": datetime.now(timezone.utc).isoformat()
                            },
                            cleanup_mode="incremental"
                        )
                    else:
                        raise HTTPException(status_code=400, detail="Web resource has no URL")
                
                # Update status to indexed
                await self.update_resource_status(
                    resource_type=resource_type.value,
                    resource_id=resource_id,
                    status=ResourceStatus.PROCESSED,
                    message="Successfully reindexed"
                )
                
                return {
                    "status": "success",
                    "message": "Resource reindexed successfully",
                    "resource_id": resource_id,
                    "resource_type": resource_type.value,
                    "result": result
                }
                
            except Exception as e:
                # Update status to failed
                await self.update_resource_status(
                    resource_type=resource_type.value,
                    resource_id=resource_id,
                    status=ResourceStatus.FAILED,
                    message=f"Reindex failed: {str(e)}"
                )
                raise
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error reindexing resource {resource_id}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Reindex failed: {str(e)}")

    async def _process_web_content_with_relationships(
        self,
        indexer: ResourceIndexer,
        url: str,
        resource_id: str,
        tenant_id: str,
        current_user: User,
        load_type: Literal["single", "recursive", "sitemap"],
        max_depth: Optional[int] = 2,
        exclude_dirs: Optional[List[str]] = None,
        sitemap_url: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Process web content and create parent-child relationships for multiple pages"""

        # For single page, use existing logic
        if load_type == "single":
            # Extract the safe metadata from the structured metadata
            safe_metadata = metadata.get("metadata", {}) if metadata else {}
            result = await indexer.index_web_content(
                url=url,
                tenant_id=tenant_id,
                resource_id=resource_id,
                load_type=load_type,
                metadata={
                    "resource_id": resource_id,
                    "tenant_id": tenant_id,
                    "processed_by": str(current_user.id),
                    "source_url": url,
                    "load_type": load_type,
                    "resource_type": metadata.get("resource_type", "web"),
                    "created_by": metadata.get("created_by"),
                    "created_at": metadata.get("created_at"),
                    **safe_metadata
                }
            )
            return {**result, "child_resources_created": 0, "total_pages_processed": 1}

        # For sitemap processing, handle the sitemap URL as root and create child entries
        elif load_type == "sitemap":
            return await self._process_sitemap_with_child_resources(
                indexer=indexer,
                sitemap_url=sitemap_url or url,
                root_resource_id=resource_id,
                tenant_id=tenant_id,
                current_user=current_user,
                metadata=metadata
            )
        
        # For recursive and sitemap, load documents first to create relationships
        documents = await indexer._load_web_content(
            url=url,
            load_type=load_type,
            max_depth=max_depth,
            exclude_dirs=exclude_dirs,
            sitemap_url=sitemap_url
        )
        
        # Find the root document (matching the original URL)
        root_doc = None
        child_docs = []
        
        for doc in documents:
            if doc.metadata.get("source") == url or doc.page_content == documents[0].page_content:
                root_doc = doc
            else:
                child_docs.append(doc)
        
        # If no root document found, use the first one
        if not root_doc and documents:
            root_doc = documents[0]
            child_docs = documents[1:]
        
        child_resources_created = 0
        root_resource_id = resource_id
        
        # Process child documents and create WebResource records
        for doc in child_docs:
            try:
                # Create child WebResource
                child_url = doc.metadata.get("source", doc.metadata.get("url", ""))
                if not child_url:
                    continue
                
                # Determine parent URL from metadata or use root URL
                parent_url = self._determine_parent_url(doc, url, documents)
                
                child_resource = WebResource(
                    id=uuid4(),
                    title=doc.metadata.get("title", f"Page from {child_url}"),
                    description=f"Child page crawled from {url}",
                    url=child_url,
                    content=doc.page_content[:1000],  # Store preview of content
                    last_crawled=datetime.now(timezone.utc),
                    crawl_depth=doc.metadata.get("depth", 1),
                    crawl_strategy=load_type,
                    parent_url=parent_url,
                    root_url=url,
                    parent_id=UUID(root_resource_id),
                    root_id=UUID(root_resource_id),
                    is_root=False,
                    page_title=doc.metadata.get("title", ""),
                    content_length=len(doc.page_content),
                    discovered_at=datetime.now(timezone.utc),
                    owner_id=current_user.id,
                    tenant_id=tenant_id,
                    status=ResourceStatus.PROCESSING
                )
                
                # Create the child resource in database
                created_child = await self.create_web_resource(child_resource, current_user, tenant_id)
                if created_child:
                    child_resources_created += 1
                    
                    # Update document metadata to include child resource ID
                    doc.metadata.update({
                        "resource_id": str(created_child.id),
                        "parent_resource_id": root_resource_id,
                        "is_child_resource": True
                    })
                    
                    logger.info(f"Created child WebResource {created_child.id} for URL: {child_url}")
                
            except Exception as e:
                logger.error(f"Failed to create child WebResource for URL {child_url}: {str(e)}")
                continue
        
        # Update metadata for all documents
        base_metadata = {
            "tenant_id": tenant_id,
            "processed_by": str(current_user.id),
            "source_url": url,
            "load_type": load_type,
            **(metadata or {})
        }
        
        # Update root document metadata
        if root_doc:
            root_doc.metadata.update({
                **base_metadata,
                "resource_id": root_resource_id,
                "is_root_resource": True,
                "child_count": child_resources_created
            })
        
        # Index all documents (root + children)
        result = await indexer.index_documents(
            documents=documents,
            cleanup_mode="incremental"
        )
        
        # Update child resource statuses to processed
        for doc in child_docs:
            child_resource_id = doc.metadata.get("resource_id")
            if child_resource_id:
                try:
                    await self.update_resource_status(
                        resource_type="web",
                        resource_id=child_resource_id,
                        status=ResourceStatus.PROCESSED,
                        message="Child page processed successfully"
                    )
                except Exception as e:
                    logger.error(f"Failed to update child resource status {child_resource_id}: {str(e)}")
        
        return {
            **result,
            "child_resources_created": child_resources_created,
            "total_pages_processed": len(documents),
            "root_resource_id": root_resource_id
        }
    
    def _determine_parent_url(self, doc: Document, root_url: str, all_docs: List[Document]) -> str:
        """Determine the parent URL for a document based on crawl hierarchy"""
        doc_url = doc.metadata.get("source", doc.metadata.get("url", ""))
        doc_depth = doc.metadata.get("depth", 1)
        
        # If depth is 1, parent is root
        if doc_depth <= 1:
            return root_url
        
        # Find potential parent by looking for documents with depth-1 that could be parent
        for other_doc in all_docs:
            other_url = other_doc.metadata.get("source", other_doc.metadata.get("url", ""))
            other_depth = other_doc.metadata.get("depth", 1)
            
            # Check if this could be a parent (depth is 1 less and URL hierarchy suggests parent)
            if other_depth == doc_depth - 1 and doc_url.startswith(other_url.rsplit('/', 1)[0]):
                return other_url
        
        # Fallback to root URL
        return root_url

    async def get_web_resource_children(self, parent_id: str, current_user: User, 
                                       skip: int = 0, limit: int = 100) -> List[WebResource]:
        """Get all child resources for a parent WebResource"""
        await self._ensure_initialized()
        
        filters = {"parent_id": parent_id}
        if current_user and current_user.tenant_id:
            filters["tenant_id"] = current_user.tenant_id
            
        return await self.web_repo.get_all(skip, limit, filters)
    
    async def get_web_resource_hierarchy(self, root_id: str, current_user: User) -> Dict[str, Any]:
        """Get the complete hierarchy for a root WebResource"""
        await self._ensure_initialized()
        
        # Get root resource
        root_resource = await self.get_web_resource(root_id)
        if not root_resource:
            return {"error": "Root resource not found"}
        
        # Get all children recursively
        children = await self._get_children_recursive(root_id, current_user)
        
        return {
            "root": root_resource,
            "children": children,
            "total_children": len(children),
            "hierarchy_depth": self._calculate_max_depth(children)
        }
    
    async def _get_children_recursive(self, parent_id: str, current_user: User) -> List[Dict[str, Any]]:
        """Recursively get all children and their children"""
        children = await self.get_web_resource_children(parent_id, current_user, limit=1000)
        
        result = []
        for child in children:
            child_dict = {
                "resource": child,
                "children": await self._get_children_recursive(str(child.id), current_user)
            }
            result.append(child_dict)
        
        return result
    
    def _calculate_max_depth(self, children: List[Dict[str, Any]], current_depth: int = 1) -> int:
        """Calculate the maximum depth of the hierarchy"""
        if not children:
            return current_depth
        
        max_depth = current_depth
        for child in children:
            child_depth = self._calculate_max_depth(child.get("children", []), current_depth + 1)
            max_depth = max(max_depth, child_depth)
        
        return max_depth
    
    async def get_web_resource_by_url(self, url: str, current_user: User) -> Optional[WebResource]:
        """Get a WebResource by its URL"""
        await self._ensure_initialized()
        
        filters = {"url": url}
        if current_user and current_user.tenant_id:
            filters["tenant_id"] = current_user.tenant_id
        
        resources = await self.web_repo.get_all(0, 1, filters)
        return resources[0] if resources else None

# Create a singleton instance for backward compatibility
resource_service = ResourceService()

# Services are now managed by ServiceManager - no singleton needed